<script setup lang="ts">
import { prjForm, prjType } from "@/utils/constant";
import { ref, watch, provide, onMounted, onUpdated } from "vue";
import type { prjInfoType, simpleSectionInfo } from "@/utils/type";
import { getProjectDetail } from "@/apis/path/createProject";
import { formatData_step1 } from "@/utils/func";
import { ElMessage } from "element-plus";
import MySteps from "@/views/createProject/components/mySteps.vue";
import PrjInfo from "@/views/common/prjInfo.vue";
import SectionSwitcher from "@/views/createProject/step3/sectionSwitcher.vue";
import VideoQuestion from "@/views/createProject/step3/videoQuestion.vue";
import ExamWrapper from "@/views/createProject/step3/examWrapper.vue";
import TextQuestion from "@/views/createProject/step3/textQuestion.vue";
import router from "@/router";

// 特别特别重要！
provide("displaying", true);

const props = defineProps({
  prjId: {
    type: Number,
    required: true,
  },
  prjForm: {
    type: String,
    required: true,
  },
});
const curPrjId = ref();
const curPrjForm = ref();
const curSecId = ref();
const prjInfoData = ref<prjInfoType>({
  disable: false,
  prjType: "",
  prjName: "",
  prjAim: "",
  prjGeneral: "",
  prjTagList: [],
  prjTargetList: [],
  prjCover: {
    echoUrl: "",
    commUrl: "",
  },
  prjAreaList: [],
});
const secList = ref<simpleSectionInfo[]>([]);
const getPrjInfo = () => {
  getProjectDetail(curPrjId.value).then((res: any) => {
    // console.log('loading: ' + curPrjId.value + ', form: ' + curPrjForm.value);
    if (res.success) {
      let baseData = res.data.list[0];
      // 这个部分后端传来的和接受的都是typeName，前端存储都转换成了typeCode
      // 因为整个后端没有统一传输到底是code还是name，所以前端自己统一一下数据便于管理
      // 在formatData_step1中，把type字段转化成typeName
      prjInfoData.value = formatData_step1(baseData);
      // FIXME: 这个地方，至少文稿类，后端确实全量返回了sectionList
      secList.value = res.data.sectionList?.map((sec: any) => {
        return {
          sectionId: sec.sectionId,
          sectionName: sec.sectionTitle,
          sectionCount: sec.sectionNum,
        };
      });
      curSecId.value = secList.value[0].sectionId;
      // editingSection.setSectionId(curSecId.value);
      // editingSection.setSectionType(curPrjForm.value, prjInfoData.value.prjType);
    } else {
      ElMessage.error("项目信息获取失败");
      router.push(`/home/<USER>
    }
  });
  //   如果这个接口就是全量信息，那么就不复用第三步的组件了，直接cv过来改逻辑
};
watch(
  () => props,
  (newVal, oldVal) => {
    if (newVal.prjId && newVal.prjForm) {
      curPrjId.value = newVal.prjId;
      curPrjForm.value = newVal.prjForm;
      getPrjInfo();
    }
  },
  { deep: true, immediate: true }
);

const handleChangeSection = (newSectionId: number) => {
  curSecId.value = newSectionId;
};
</script>

<template>
  <div class="prj-info-container">
    <div>
      <div class="prjTitle">
        <span class="form" v-if="props.prjForm == '1'">
          <img src="@/assets/images/create/u3454.svg" />
          <span style="margin-left: 5px">视频项目</span>
        </span>
        <span class="form" v-else-if="props.prjForm == '2'">
          <img src="@/assets/images/create/u3462.svg" />
          <span style="margin-left: 5px">文稿项目</span>
        </span>
      </div>
      <span class="form">
        <img src="@/assets/images/back/cancelPub.svg" />
        <span>撤回</span>
      </span>
    </div>
    <div class="line"></div>
    <div class="content-wrapper">
      <prj-info :info-data="prjInfoData" :showTarget="true"></prj-info>
      <!-- TODO: 突然觉得可以提一个函数来做是否多节的判断 -->
      <template
        v-if="
          prjInfoData.prjType == prjType.prj ||
          prjInfoData.prjType == prjType.area ||
          (prjInfoData.prjType == prjType.exam && curPrjForm == prjForm.text)
        "
      >
        <section-switcher
          @changeSection="handleChangeSection"
          :section-list="secList"
          :cur-section-id="curSecId"
        ></section-switcher>
      </template>
      <template v-if="curPrjForm == prjForm.video">
        <!-- TODO: 这里可能要改，因为展示的视频在上边，创三的在下边 -->
        <video-question
          :cur-project-id="curPrjId"
          :cur-section-id="curSecId"
        ></video-question>
      </template>
      <!-- <template v-else-if="curPrjForm == prjForm.text && prjInfoData.prjType == prjType.exam">
        <exam-wrapper :cur-project-id="curPrjId" :cur-section-id="curSecId"></exam-wrapper>
      </template> -->
      <template v-else-if="curPrjForm == prjForm.text">
        <text-question
          :cur-project-id="curPrjId"
          :cur-section-id="curSecId"
        ></text-question>
      </template>
    </div>
  </div>
</template>

<style scoped>
.prj-info-container {
  font-family: var(--font-family-text);
  background-color: white;
  color: var(--color-black);
  width: 100%;
  max-width: var(--width-content);
  min-height: 100vh;
  /* margin: 20px auto auto auto; */
  display: flex;
  flex-direction: column;
  align-items: center;
  .form {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: flex-start;
    color: var(--color-primary);
    font-weight: 600;
    margin: 10px 40px;
    padding-left: 20px;
  }
  .content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 30px;
  }
}
</style>
